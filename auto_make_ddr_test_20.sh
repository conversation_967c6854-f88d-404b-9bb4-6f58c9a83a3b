#!/bin/bash

# 定义变量
REPO_URLS=(
http://172.16.0.250/APP-Private/Standard_Teset/Import-testing-tool/ddr_test.git
)

BRANCH="master"
USERNAME="shangna"  # 替换为您的用户名
PASSWORD="*Nashang2004*"  # 替换为您的密码

# 遍历并下载所有仓库
clone_all_repos() {
    for repo_url in "${REPO_URLS[@]}"; do
        # 从URL中提取目录名
        repo_name=$(basename "$repo_url" .git)
        repo_dir=$(dirname "$repo_url" | awk -F'/' '{print $NF}')
        target_dir="${repo_dir}_${repo_name}"
        
        echo "正在克隆仓库: $repo_url 到 $target_dir"

        # 检查目标目录是否已存在
        if [ -d "$target_dir" ]; then
            echo "目标目录 $LOCAL_DIR 已存在，正在删除..."
            rm -rf "$target_dir"
        fi
        
        # 添加用户名和密码到URL
        repo_url_with_cred="http://${USERNAME}:${PASSWORD}@${repo_url#http://}"
        
        # 克隆仓库
        git clone "$repo_url_with_cred" "$target_dir"
        
        # 检查克隆是否成功
        if [ $? -ne 0 ]; then
            echo "克隆仓库 $repo_url 失败"
        else
            echo "成功克隆仓库 $repo_url 到 $target_dir"
        fi

        # 进入仓库目录
        cd "$target_dir"

        # 检查develop分支是否存在
        if git branch -a | grep -q "remotes/origin/$BRANCH"; then
            echo "切换到 $BRANCH 分支..."
            git checkout "$BRANCH"
            
            if [ $? -ne 0 ]; then
                echo "切换到 $BRANCH 分支失败"
                exit 1
            fi
        else
            echo "分支 $BRANCH 不存在"
            exit 1
        fi

        # 确认当前分支
        CURRENT_BRANCH=$(git branch --show-current)
        echo "当前分支: $CURRENT_BRANCH"

        # 输出成功信息
        echo "成功克隆仓库并切换到 $BRANCH 分支"
        echo "源码已下载到 $(pwd) 目录"

        # 列出文件
        echo "仓库内容:"
        ls -la
		cd ../
    done
}

# 编译所有Makefile并拷贝结果
compile_and_copy() {
    echo "开始查找、编译所有Makefile并拷贝结果..."

    # 初始化成功和失败计数器
    local fail_count=0
    
    # 创建目标根目录
    cp -a tester_src/. $output_dir
    TARGET_ROOT="$output_dir/target/item"
    #mkdir -p "$TARGET_ROOT"
    
    # 查找所有Makefile
    MAKEFILES=$(find . -name "Makefile")
    
    # 遍历所有找到的Makefile
    for makefile in $MAKEFILES; do
        makefile_dir=$(dirname "$makefile")
        echo "正在编译: $makefile"
        
        # 进入Makefile所在目录
        cd "$makefile_dir"
        
        # 执行make
        make clean

        # 执行make
        make
        
        # 检查make是否成功
        if [ $? -ne 0 ]; then
            echo "编译 $makefile 失败"
             ((fail_count++))
        else
            echo "成功编译 $makefile"
            
            # 分析路径，确定目标目录
				if [[ "$makefile" == *"$src_path"* ]]; then
                    
                    # 查找可执行文件并复制
                    executables=$(find . -type f -executable -not -path "*.git*" | xargs file | grep "ELF" | cut -d: -f1)
                    if [ -n "$executables" ]; then
                    for exe in $executables; do
                        echo "复制可执行文件 $exe 到 $TARGET_ROOT/"
                        cp "$exe" "$TARGET_ROOT/"
                    done
                else
                    echo "在 $makefile_dir 中未找到可执行二进制文件"
                fi                    
                    break
                fi
        fi
        
        # 返回原目录
        cd - > /dev/null
    done
    
    echo "所有Makefile编译完成，结果已复制到对应目录"
    echo "失败次数: $fail_count"

    # 查找并复制所有shell脚本
    echo "开始查找并复制所有shell脚本..."
    # 遍历下载的仓库目录
    echo "仓库目录: $target_dir"
    if [ -d "$target_dir" ]; then
        echo "正在从 $SRC_DIR 复制所有 .sh 文件到 $TARGET_ROOT"
        # 使用 find + cp 扁平化复制
        find "$SRC_DIR" -name "*.sh" -type f -exec cp -v {} "$TARGET_ROOT/" \;
    
        # 添加执行权限
        find "$TARGET_ROOT" -name "*.sh" -type f -exec chmod -v +x {} \;
        echo "复制完成！目标路径: $TARGET_ROOT"
    fi

    return 0
}

# 在克隆完所有仓库后调用编译和复制函数
clone_all_repos

# 初始化变量
declare -A gcc_map
key_order=()

# 在脚本开始处添加日志文件路径
LOG_FILE="compile_result.log"
# 清空日志文件
> "$LOG_FILE"

# Parse gcc_all.txt file
while IFS= read -r line || [ -n "$line" ]; do
    # Skip if line starts with "#classify"
    if [[ "$line" =~ ^#classify ]]; then
        break  # Exit the loop when reaching classify section
    fi

    # 跳过空行，同时保存当前的键值对
    if [[ -z "$line" ]]; then
        if [[ -n "$current_key" && -n "$current_value" ]]; then
            gcc_map["$current_key"]="$current_value"
            current_key=""
            current_value=""
        fi
        continue
    fi

    # 如果行以#开头，表示这是一个新的键
    if [[ "$line" =~ ^#(.+) ]]; then
        # 保存之前的键值对（如果存在）
        if [[ -n "$current_key" && -n "$current_value" ]]; then
            gcc_map["$current_key"]="$current_value"
            current_value=""
        fi
        # 提取键（#后面到第一个空格之前的内容）
        current_key=$(echo "${BASH_REMATCH[1]}" | cut -d' ' -f1)
        # 将新的键添加到顺序数组中
        key_order+=("$current_key")
    else
        # 将行添加到当前值中，保持换行符
        if [[ -n "$current_value" ]]; then
            current_value+=$'\n'
        fi
        current_value+="$line"
    fi
done < gcc_all_20.txt

# 保存最后一个键值对（如果存在）
if [[ -n "$current_key" && -n "$current_value" ]]; then
    gcc_map["$current_key"]="$current_value"
fi
keys_to_process=("${key_order[@]}")
# 获取平台对应的基础输出目录
get_output_base_dir() {
    local name=$1
    # RK platforms
    if [[ "$name" =~ ^(33|35) ]]; then
        echo "RK"
    # NXP platforms
    elif [[ "$name" =~ ^(6u|6q|6x|8m|93|10|91) ]]; then
        echo "NXP"
    # ALLWINNER platforms
    elif [[ "$name" =~ ^(T|A40) ]]; then
        echo "ALLWINNER"
    # LOONGSON platforms
    elif [[ "$name" =~ ^(loo) ]]; then
        echo "LOONGSON"
    # NUVOTON platforms
    elif [[ "$name" =~ ^(MA) ]]; then
        echo "NUVOTON"
    # RENESAS platforms
    elif [[ "$name" =~ ^(g2) ]]; then
        echo "RENESAS"
    # SEMDRVE platforms
    elif [[ "$name" =~ ^(D9) ]]; then
        echo "SEMDRVE"
    # TI platforms
    elif [[ "$name" =~ ^(62) ]]; then
        echo "TI"
    # Other platforms
    else
        echo "other"
    fi
}

# 编译所有版本
echo "开始编译所有版本..."
for key in "${keys_to_process[@]}"; do
    echo "----------------------------------------"
    echo "正在编译 $key 版本..."
    echo "设置环境变量..."

    # 确定输出目录
    base_dir=$(get_output_base_dir "$key")
    output_dir="/home/<USER>/app/ddr_test/ddr_test_src/${base_dir}/${key}"
    mkdir -p "$output_dir"
    upload_dir="/home/<USER>/app/git_lab/ddr_test_bin/${base_dir}/${key}"
    mkdir -p "$upload_dir"

    # 创建新的shell环境执行编译，避免环境变量污染
    (
        # 执行环境设置命令
        eval "${gcc_map[$key]}"

        # 编译
        compile_and_copy
        if [ $? -eq 0 ]; then
            cp -r /home/<USER>/tool/${key}/tool $output_dir/target/
            echo "/home/<USER>/tool/${key}/tool 已复制到 $output_dir/target/"
            tar -cvf "$output_dir/target.tar" -C "$output_dir/target" .
            cat $output_dir/install.sh $output_dir/target.tar > $output_dir/ddr_test.bin
            cp $output_dir/ddr_test.bin $upload_dir/ddr_test.bin

            message="✓ $key 打包成功，已移动到 $output_dir/"
            echo "$message"
            echo "$message" >> "$LOG_FILE"
        else
            message="✗ $key 版本编译失败"
            echo "$message"
            echo "$message" >> "$LOG_FILE"
        fi

    )
    echo "----------------------------------------"
done


